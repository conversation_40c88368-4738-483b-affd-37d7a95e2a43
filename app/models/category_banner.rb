class CategoryBanner < ActiveRecord::Base
    belongs_to :category
    has_attached_file :photo, 
    styles: {
      main: "945x200",
      main_webp: ["945x200", :webp],
      long_webp: ['337x507#', :webp]
    },
    :s3_host_alias => if Rails.env.production?
                        Proc.new {|a| "assets#{a.size % 6}.mirraw.com" }
                      else
                        'mirraw-test.s3.amazonaws.com'
                      end
    scope :mobile_source, -> {where("app_source LIKE ?",'%web%')}
    scope :graded, -> { order('category_banners.grade ASC') }
    scope :country, -> (country_code) do
      where("country LIKE ?", "%#{country_code}%")
    end
    scope :category_id, -> (category_id) do
      where("category_ids LIKE ?", "%#{category_id}%")
    end


    def self.category_banner_for(country_code, category=nil)
      Rails.cache.fetch("category-banners-#{category.id}-#{country_code}", :expires_in => 24.hours) do
        if category.present?
          banners = get_banners_for_category(country_code,category)
          if banners.present?
            return banners
          end
        end
      end
    end

    def self.get_banners_for_category(country_code,category)
      CategoryBanner.mobile_source
                    .graded
                    .country(country_code)
                    .category_id(category.id)
                    .where('start_date <= ?', Time.now)
                    .where('end_date >= ?', Time.now)
                    .group_by(&:position)
    end
end

