class ReviewsController < ApplicationController
  before_filter :authenticate_account!, only: [:save_review]

  def site_review
    if request.post?
      response = handle_post_site_review
      render json: response
    else
      load_site_review_data
    end
  end

  private

  def handle_post_site_review
    response = {error: 'something went wrong'}

    if params[:review].present? && params[:star].present?
      response = create_or_update_site_review
    elsif (review = Review.where(id: params[:review_id]).first).present?
      response = handle_review_resolution(review)
    end

    response
  end

  def create_or_update_site_review
    if current_account.present? && (@review = Review.where(user_id: current_account.try(:accountable_id), site_review: true).first).present?
      update_existing_site_review
    else
      create_new_site_review
    end

    @review.assured = nil
    @review.save
    {success: true}
  end

  def update_existing_site_review
    @review.review = params[:review] unless params[:review].blank?
    @review.rating = params[:star]
    @review.approved = true
  end

  def create_new_site_review
    @review = Review.new(review: params[:review], rating: params[:star], site_review: true)
    unless can? :manage, @review
      @review.user_id = current_account.try(:accountable_id)
    else
      @review.system_user = true
    end
  end

  def handle_review_resolution(review)
    params[:resolved] == 'true' ? review.update_column(:assured, 'Resolved') : review.update_column(:approved, false)
    {success: true}
  end

  def load_site_review_data
    @integration_status = 'new'
    @kind = 'review_page'
    @seo = SeoList.where(label: @kind).first

    # Use refactored model methods for better performance
    @site_reviews = Review.site_reviews_for_page(page: params[:site_page], per_page: 10)
    @reviews = Review.product_reviews_for_page(page: params[:page], per_page: 10)
    @best_reviewed_designs = Review.best_reviewed_designs_for_page(page: params[:reviewed_best], per_page: 10)

    if (user_id = current_account.try(:accountable_id)).present? && !current_account.admin?
      @review = Review.site.where(user_id: user_id).first
    end
  end

  # Create review of a particular design for a particular user or update it if already exists
  # == Input: 
  # POST
  #   Params[:rating], params[review], params[design_id]
  # == Returns:
  # JSON
  def save_review
    if (design = Design.find_by_id(params[:design_id])).present?
      begin
        review = current_account.user.reviews.where(design_id: design.id, designer_id: design.designer_id).first_or_initialize
        review.review = params[:review].to_s unless params[:review] == 'false'
        review.rating = params[:rating].to_i
        review.user_id = current_account.try(:accountable_id)
        review.save!
      rescue
        render json: { message: 'Cannot review this product', status: 422}
      end
      render json: { message: 'Review saved successfully', status: 200}
    else
      render json: { message: 'Unable to save a review', status: 422}
    end
  end
end
